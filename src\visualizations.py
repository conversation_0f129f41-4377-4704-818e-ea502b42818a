"""
Visualization utilities for demand forecasting analysis.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import List, Dict, Optional, Tuple
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ForecastingVisualizer:
    """Class for creating comprehensive visualizations for demand forecasting."""
    
    def __init__(self, figsize: Tuple[int, int] = (15, 10)):
        """
        Initialize the visualizer.
        
        Args:
            figsize (Tuple[int, int]): Default figure size for matplotlib plots
        """
        self.figsize = figsize
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
        
        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
    
    def plot_sales_overview(self, df: pd.DataFrame, save_path: Optional[str] = None) -> None:
        """
        Create an overview of sales patterns.
        
        Args:
            df (pd.DataFrame): Sales dataframe
            save_path (Optional[str]): Path to save the plot
        """
        fig, axes = plt.subplots(2, 2, figsize=(20, 15))
        fig.suptitle('Sales Data Overview', fontsize=16, fontweight='bold')
        
        # 1. Daily sales trend
        daily_sales = df.groupby('sale_date')['quantity_sold'].sum().reset_index()
        axes[0, 0].plot(daily_sales['sale_date'], daily_sales['quantity_sold'], alpha=0.7)
        axes[0, 0].set_title('Daily Total Sales Trend')
        axes[0, 0].set_xlabel('Date')
        axes[0, 0].set_ylabel('Total Quantity Sold')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 2. Sales by category
        category_sales = df.groupby('category')['quantity_sold'].sum().sort_values(ascending=False)
        axes[0, 1].bar(category_sales.index, category_sales.values)
        axes[0, 1].set_title('Total Sales by Category')
        axes[0, 1].set_xlabel('Category')
        axes[0, 1].set_ylabel('Total Quantity Sold')
        
        # 3. Monthly sales pattern
        df['year_month'] = df['sale_date'].dt.to_period('M')
        monthly_sales = df.groupby('year_month')['quantity_sold'].sum()
        axes[1, 0].plot(range(len(monthly_sales)), monthly_sales.values, marker='o')
        axes[1, 0].set_title('Monthly Sales Pattern')
        axes[1, 0].set_xlabel('Month')
        axes[1, 0].set_ylabel('Total Quantity Sold')
        axes[1, 0].set_xticks(range(0, len(monthly_sales), 6))
        axes[1, 0].set_xticklabels([str(monthly_sales.index[i]) for i in range(0, len(monthly_sales), 6)], rotation=45)
        
        # 4. Day of week pattern
        dow_sales = df.groupby('day_of_week')['quantity_sold'].mean()
        dow_names = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        axes[1, 1].bar(dow_names, dow_sales.values)
        axes[1, 1].set_title('Average Sales by Day of Week')
        axes[1, 1].set_xlabel('Day of Week')
        axes[1, 1].set_ylabel('Average Quantity Sold')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_saudi_calendar_effects(self, df: pd.DataFrame, save_path: Optional[str] = None) -> None:
        """
        Visualize the effects of Saudi calendar events on sales.
        
        Args:
            df (pd.DataFrame): Sales dataframe with Saudi calendar features
            save_path (Optional[str]): Path to save the plot
        """
        fig, axes = plt.subplots(2, 2, figsize=(20, 15))
        fig.suptitle('Saudi Calendar Effects on Sales', fontsize=16, fontweight='bold')
        
        # 1. Ramadan effect
        if 'is_ramadan' in df.columns:
            ramadan_sales = df.groupby('is_ramadan')['quantity_sold'].mean()
            axes[0, 0].bar(['Non-Ramadan', 'Ramadan'], ramadan_sales.values, 
                          color=['lightblue', 'orange'])
            axes[0, 0].set_title('Average Sales: Ramadan vs Non-Ramadan')
            axes[0, 0].set_ylabel('Average Quantity Sold')
            
            # Add percentage change
            pct_change = (ramadan_sales[1] - ramadan_sales[0]) / ramadan_sales[0] * 100
            axes[0, 0].text(0.5, max(ramadan_sales.values) * 0.8, 
                           f'Change: {pct_change:.1f}%', 
                           ha='center', fontweight='bold')
        
        # 2. Eid effects
        if 'is_any_eid' in df.columns:
            eid_sales = df.groupby('is_any_eid')['quantity_sold'].mean()
            axes[0, 1].bar(['Non-Eid', 'Eid'], eid_sales.values, 
                          color=['lightgreen', 'red'])
            axes[0, 1].set_title('Average Sales: Eid vs Non-Eid')
            axes[0, 1].set_ylabel('Average Quantity Sold')
            
            # Add percentage change
            pct_change = (eid_sales[1] - eid_sales[0]) / eid_sales[0] * 100
            axes[0, 1].text(0.5, max(eid_sales.values) * 0.8, 
                           f'Change: {pct_change:.1f}%', 
                           ha='center', fontweight='bold')
        
        # 3. Ramadan phase effects
        if 'ramadan_phase' in df.columns:
            phase_sales = df.groupby('ramadan_phase')['quantity_sold'].mean().sort_values(ascending=False)
            axes[1, 0].bar(phase_sales.index, phase_sales.values)
            axes[1, 0].set_title('Average Sales by Ramadan Phase')
            axes[1, 0].set_ylabel('Average Quantity Sold')
            axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 4. Pre-Eid shopping surge
        if 'pre_eid_shopping' in df.columns:
            pre_eid_sales = df.groupby('pre_eid_shopping')['quantity_sold'].mean()
            axes[1, 1].bar(['Normal', 'Pre-Eid Shopping'], pre_eid_sales.values, 
                          color=['lightcoral', 'gold'])
            axes[1, 1].set_title('Pre-Eid Shopping Surge Effect')
            axes[1, 1].set_ylabel('Average Quantity Sold')
            
            # Add percentage change
            pct_change = (pre_eid_sales[1] - pre_eid_sales[0]) / pre_eid_sales[0] * 100
            axes[1, 1].text(0.5, max(pre_eid_sales.values) * 0.8, 
                           f'Surge: {pct_change:.1f}%', 
                           ha='center', fontweight='bold')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_product_performance(self, df: pd.DataFrame, top_n: int = 10, 
                                save_path: Optional[str] = None) -> None:
        """
        Visualize top performing products and categories.
        
        Args:
            df (pd.DataFrame): Sales dataframe
            top_n (int): Number of top products to show
            save_path (Optional[str]): Path to save the plot
        """
        fig, axes = plt.subplots(2, 2, figsize=(20, 15))
        fig.suptitle('Product Performance Analysis', fontsize=16, fontweight='bold')
        
        # 1. Top products by total sales
        product_sales = df.groupby('product_id')['quantity_sold'].sum().sort_values(ascending=False).head(top_n)

        # Get category and brand info for better labeling
        product_info = df.groupby('product_id').agg({
            'category': 'first',
            'brand': 'first'
        })

        # Create better labels
        product_labels = []
        for pid in product_sales.index:
            if pid in product_info.index:
                category = product_info.loc[pid, 'category']
                brand = product_info.loc[pid, 'brand']
                label = f'ID:{pid}\n({category}-{brand})'
            else:
                label = f'Product {pid}'
            product_labels.append(label)

        axes[0, 0].barh(range(len(product_sales)), product_sales.values)
        axes[0, 0].set_yticks(range(len(product_sales)))
        axes[0, 0].set_yticklabels(product_labels, fontsize=8)
        axes[0, 0].set_title(f'Top {top_n} Products by Total Sales')
        axes[0, 0].set_xlabel('Total Quantity Sold')
        
        # 2. Category performance
        category_stats = df.groupby('category').agg({
            'quantity_sold': ['sum', 'mean', 'std']
        }).round(2)
        category_stats.columns = ['Total_Sales', 'Avg_Sales', 'Std_Sales']
        category_stats = category_stats.sort_values('Total_Sales', ascending=False)
        
        x = range(len(category_stats))
        axes[0, 1].bar(x, category_stats['Total_Sales'], alpha=0.7, label='Total Sales')
        axes[0, 1].set_title('Sales Performance by Category')
        axes[0, 1].set_xlabel('Category')
        axes[0, 1].set_ylabel('Total Quantity Sold')
        axes[0, 1].set_xticks(x)
        axes[0, 1].set_xticklabels(category_stats.index, rotation=45)
        
        # 3. Brand performance (top brands)
        brand_sales = df.groupby('brand')['quantity_sold'].sum().sort_values(ascending=False).head(top_n)
        axes[1, 0].barh(range(len(brand_sales)), brand_sales.values)
        axes[1, 0].set_yticks(range(len(brand_sales)))
        axes[1, 0].set_yticklabels(brand_sales.index)
        axes[1, 0].set_title(f'Top {top_n} Brands by Total Sales')
        axes[1, 0].set_xlabel('Total Quantity Sold')
        
        # 4. Sales distribution
        axes[1, 1].hist(df['quantity_sold'], bins=50, alpha=0.7, edgecolor='black')
        axes[1, 1].set_title('Distribution of Daily Sales Quantities')
        axes[1, 1].set_xlabel('Quantity Sold')
        axes[1, 1].set_ylabel('Frequency')
        axes[1, 1].axvline(df['quantity_sold'].mean(), color='red', linestyle='--', 
                          label=f'Mean: {df["quantity_sold"].mean():.1f}')
        axes[1, 1].legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_seasonal_patterns(self, df: pd.DataFrame, save_path: Optional[str] = None) -> None:
        """
        Analyze and visualize seasonal patterns in sales data.
        
        Args:
            df (pd.DataFrame): Sales dataframe
            save_path (Optional[str]): Path to save the plot
        """
        fig, axes = plt.subplots(2, 2, figsize=(20, 15))
        fig.suptitle('Seasonal Patterns in Sales', fontsize=16, fontweight='bold')
        
        # 1. Monthly seasonality
        monthly_avg = df.groupby('month')['quantity_sold'].mean()
        month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        axes[0, 0].plot(monthly_avg.index, monthly_avg.values, marker='o', linewidth=2)
        axes[0, 0].set_title('Average Sales by Month')
        axes[0, 0].set_xlabel('Month')
        axes[0, 0].set_ylabel('Average Quantity Sold')
        axes[0, 0].set_xticks(range(1, 13))
        axes[0, 0].set_xticklabels(month_names)
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Quarterly patterns
        quarterly_avg = df.groupby('quarter')['quantity_sold'].mean()
        axes[0, 1].bar(quarterly_avg.index, quarterly_avg.values, 
                      color=['lightblue', 'lightgreen', 'orange', 'lightcoral'])
        axes[0, 1].set_title('Average Sales by Quarter')
        axes[0, 1].set_xlabel('Quarter')
        axes[0, 1].set_ylabel('Average Quantity Sold')
        axes[0, 1].set_xticks(range(1, 5))
        axes[0, 1].set_xticklabels(['Q1', 'Q2', 'Q3', 'Q4'])
        
        # 3. Day of week patterns
        dow_avg = df.groupby('day_of_week')['quantity_sold'].mean()
        dow_names = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        axes[1, 0].bar(range(7), dow_avg.values)
        axes[1, 0].set_title('Average Sales by Day of Week')
        axes[1, 0].set_xlabel('Day of Week')
        axes[1, 0].set_ylabel('Average Quantity Sold')
        axes[1, 0].set_xticks(range(7))
        axes[1, 0].set_xticklabels(dow_names)
        
        # 4. Weekend vs weekday
        weekend_comparison = df.groupby('is_weekend')['quantity_sold'].mean()
        axes[1, 1].bar(['Weekday', 'Weekend'], weekend_comparison.values, 
                      color=['skyblue', 'orange'])
        axes[1, 1].set_title('Weekday vs Weekend Sales')
        axes[1, 1].set_ylabel('Average Quantity Sold')
        
        # Add percentage difference
        pct_diff = (weekend_comparison[1] - weekend_comparison[0]) / weekend_comparison[0] * 100
        axes[1, 1].text(0.5, max(weekend_comparison.values) * 0.8, 
                       f'Weekend vs Weekday: {pct_diff:+.1f}%', 
                       ha='center', fontweight='bold')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_interactive_dashboard(self, df: pd.DataFrame) -> None:
        """
        Create an interactive dashboard using Plotly.
        
        Args:
            df (pd.DataFrame): Sales dataframe
        """
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Daily Sales Trend', 'Category Performance', 
                          'Monthly Patterns', 'Ramadan Effects'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # 1. Daily sales trend
        daily_sales = df.groupby('sale_date')['quantity_sold'].sum().reset_index()
        fig.add_trace(
            go.Scatter(x=daily_sales['sale_date'], y=daily_sales['quantity_sold'],
                      mode='lines', name='Daily Sales'),
            row=1, col=1
        )
        
        # 2. Category performance
        category_sales = df.groupby('category')['quantity_sold'].sum().reset_index()
        fig.add_trace(
            go.Bar(x=category_sales['category'], y=category_sales['quantity_sold'],
                  name='Category Sales'),
            row=1, col=2
        )
        
        # 3. Monthly patterns
        monthly_avg = df.groupby('month')['quantity_sold'].mean().reset_index()
        fig.add_trace(
            go.Scatter(x=monthly_avg['month'], y=monthly_avg['quantity_sold'],
                      mode='lines+markers', name='Monthly Average'),
            row=2, col=1
        )
        
        # 4. Ramadan effects (if available)
        if 'is_ramadan' in df.columns:
            ramadan_effect = df.groupby('is_ramadan')['quantity_sold'].mean().reset_index()
            ramadan_effect['period'] = ramadan_effect['is_ramadan'].map({0: 'Non-Ramadan', 1: 'Ramadan'})
            fig.add_trace(
                go.Bar(x=ramadan_effect['period'], y=ramadan_effect['quantity_sold'],
                      name='Ramadan Effect'),
                row=2, col=2
            )
        
        # Update layout
        fig.update_layout(
            title_text="Sales Analytics Dashboard",
            showlegend=False,
            height=800
        )
        
        fig.show()
    
    def plot_correlation_matrix(self, df: pd.DataFrame, features: Optional[List[str]] = None,
                               save_path: Optional[str] = None) -> None:
        """
        Plot correlation matrix of numerical features.
        
        Args:
            df (pd.DataFrame): Sales dataframe
            features (Optional[List[str]]): Specific features to include
            save_path (Optional[str]): Path to save the plot
        """
        if features is None:
            # Select numerical columns
            numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            # Remove ID columns
            features = [col for col in numerical_cols if 'id' not in col.lower()]
        
        # Calculate correlation matrix
        corr_matrix = df[features].corr()
        
        # Create plot
        plt.figure(figsize=(12, 10))
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": 0.8})
        plt.title('Feature Correlation Matrix')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
