"{\"growth\": \"linear\", \"n_changepoints\": 15, \"specified_changepoints\": false, \"changepoint_range\": 0.8, \"yearly_seasonality\": \"auto\", \"weekly_seasonality\": \"auto\", \"daily_seasonality\": \"auto\", \"seasonality_mode\": \"additive\", \"seasonality_prior_scale\": 10.0, \"changepoint_prior_scale\": 0.05, \"holidays_prior_scale\": 10.0, \"mcmc_samples\": 0, \"interval_width\": 0.8, \"uncertainty_samples\": 1000, \"y_scale\": 19.0, \"logistic_floor\": false, \"country_holidays\": null, \"component_modes\": {\"additive\": [\"weekly\", \"additive_terms\", \"extra_regressors_additive\", \"holidays\"], \"multiplicative\": [\"multiplicative_terms\", \"extra_regressors_multiplicative\"]}, \"changepoints\": \"{\\\"name\\\":\\\"ds\\\",\\\"index\\\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15],\\\"data\\\":[\\\"2020-01-02T00:00:00.000Z\\\",\\\"2020-01-03T00:00:00.000Z\\\",\\\"2020-01-04T00:00:00.000Z\\\",\\\"2020-01-05T00:00:00.000Z\\\",\\\"2020-01-06T00:00:00.000Z\\\",\\\"2020-01-07T00:00:00.000Z\\\",\\\"2020-01-08T00:00:00.000Z\\\",\\\"2020-01-09T00:00:00.000Z\\\",\\\"2020-01-10T00:00:00.000Z\\\",\\\"2020-01-11T00:00:00.000Z\\\",\\\"2020-01-12T00:00:00.000Z\\\",\\\"2020-01-13T00:00:00.000Z\\\",\\\"2020-01-14T00:00:00.000Z\\\",\\\"2020-01-15T00:00:00.000Z\\\",\\\"2020-01-16T00:00:00.000Z\\\"]}\", \"history_dates\": \"{\\\"name\\\":\\\"ds\\\",\\\"index\\\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19],\\\"data\\\":[\\\"2020-01-01T00:00:00.000Z\\\",\\\"2020-01-02T00:00:00.000Z\\\",\\\"2020-01-03T00:00:00.000Z\\\",\\\"2020-01-04T00:00:00.000Z\\\",\\\"2020-01-05T00:00:00.000Z\\\",\\\"2020-01-06T00:00:00.000Z\\\",\\\"2020-01-07T00:00:00.000Z\\\",\\\"2020-01-08T00:00:00.000Z\\\",\\\"2020-01-09T00:00:00.000Z\\\",\\\"2020-01-10T00:00:00.000Z\\\",\\\"2020-01-11T00:00:00.000Z\\\",\\\"2020-01-12T00:00:00.000Z\\\",\\\"2020-01-13T00:00:00.000Z\\\",\\\"2020-01-14T00:00:00.000Z\\\",\\\"2020-01-15T00:00:00.000Z\\\",\\\"2020-01-16T00:00:00.000Z\\\",\\\"2020-01-17T00:00:00.000Z\\\",\\\"2020-01-18T00:00:00.000Z\\\",\\\"2020-01-19T00:00:00.000Z\\\",\\\"2020-01-20T00:00:00.000Z\\\"]}\", \"train_holiday_names\": null, \"start\": 1577836800.0, \"t_scale\": 1641600.0, \"holidays\": null, \"history\": \"{\\\"schema\\\":{\\\"fields\\\":[{\\\"name\\\":\\\"ds\\\",\\\"type\\\":\\\"string\\\"},{\\\"name\\\":\\\"y\\\",\\\"type\\\":\\\"integer\\\"},{\\\"name\\\":\\\"floor\\\",\\\"type\\\":\\\"integer\\\"},{\\\"name\\\":\\\"t\\\",\\\"type\\\":\\\"number\\\"},{\\\"name\\\":\\\"y_scaled\\\",\\\"type\\\":\\\"number\\\"}],\\\"pandas_version\\\":\\\"1.4.0\\\"},\\\"data\\\":[{\\\"ds\\\":\\\"2020-01-01T00:00:00.000Z\\\",\\\"y\\\":0,\\\"floor\\\":0,\\\"t\\\":0.0,\\\"y_scaled\\\":0.0},{\\\"ds\\\":\\\"2020-01-02T00:00:00.000Z\\\",\\\"y\\\":1,\\\"floor\\\":0,\\\"t\\\":0.0526315789,\\\"y_scaled\\\":0.0526315789},{\\\"ds\\\":\\\"2020-01-03T00:00:00.000Z\\\",\\\"y\\\":2,\\\"floor\\\":0,\\\"t\\\":0.1052631579,\\\"y_scaled\\\":0.1052631579},{\\\"ds\\\":\\\"2020-01-04T00:00:00.000Z\\\",\\\"y\\\":3,\\\"floor\\\":0,\\\"t\\\":0.1578947368,\\\"y_scaled\\\":0.1578947368},{\\\"ds\\\":\\\"2020-01-05T00:00:00.000Z\\\",\\\"y\\\":4,\\\"floor\\\":0,\\\"t\\\":0.2105263158,\\\"y_scaled\\\":0.2105263158},{\\\"ds\\\":\\\"2020-01-06T00:00:00.000Z\\\",\\\"y\\\":5,\\\"floor\\\":0,\\\"t\\\":0.2631578947,\\\"y_scaled\\\":0.2631578947},{\\\"ds\\\":\\\"2020-01-07T00:00:00.000Z\\\",\\\"y\\\":6,\\\"floor\\\":0,\\\"t\\\":0.3157894737,\\\"y_scaled\\\":0.3157894737},{\\\"ds\\\":\\\"2020-01-08T00:00:00.000Z\\\",\\\"y\\\":7,\\\"floor\\\":0,\\\"t\\\":0.3684210526,\\\"y_scaled\\\":0.3684210526},{\\\"ds\\\":\\\"2020-01-09T00:00:00.000Z\\\",\\\"y\\\":8,\\\"floor\\\":0,\\\"t\\\":0.4210526316,\\\"y_scaled\\\":0.4210526316},{\\\"ds\\\":\\\"2020-01-10T00:00:00.000Z\\\",\\\"y\\\":9,\\\"floor\\\":0,\\\"t\\\":0.4736842105,\\\"y_scaled\\\":0.4736842105},{\\\"ds\\\":\\\"2020-01-11T00:00:00.000Z\\\",\\\"y\\\":10,\\\"floor\\\":0,\\\"t\\\":0.5263157895,\\\"y_scaled\\\":0.5263157895},{\\\"ds\\\":\\\"2020-01-12T00:00:00.000Z\\\",\\\"y\\\":11,\\\"floor\\\":0,\\\"t\\\":0.5789473684,\\\"y_scaled\\\":0.5789473684},{\\\"ds\\\":\\\"2020-01-13T00:00:00.000Z\\\",\\\"y\\\":12,\\\"floor\\\":0,\\\"t\\\":0.6315789474,\\\"y_scaled\\\":0.6315789474},{\\\"ds\\\":\\\"2020-01-14T00:00:00.000Z\\\",\\\"y\\\":13,\\\"floor\\\":0,\\\"t\\\":0.6842105263,\\\"y_scaled\\\":0.6842105263},{\\\"ds\\\":\\\"2020-01-15T00:00:00.000Z\\\",\\\"y\\\":14,\\\"floor\\\":0,\\\"t\\\":0.7368421053,\\\"y_scaled\\\":0.7368421053},{\\\"ds\\\":\\\"2020-01-16T00:00:00.000Z\\\",\\\"y\\\":15,\\\"floor\\\":0,\\\"t\\\":0.7894736842,\\\"y_scaled\\\":0.7894736842},{\\\"ds\\\":\\\"2020-01-17T00:00:00.000Z\\\",\\\"y\\\":16,\\\"floor\\\":0,\\\"t\\\":0.8421052632,\\\"y_scaled\\\":0.8421052632},{\\\"ds\\\":\\\"2020-01-18T00:00:00.000Z\\\",\\\"y\\\":17,\\\"floor\\\":0,\\\"t\\\":0.8947368421,\\\"y_scaled\\\":0.8947368421},{\\\"ds\\\":\\\"2020-01-19T00:00:00.000Z\\\",\\\"y\\\":18,\\\"floor\\\":0,\\\"t\\\":0.9473684211,\\\"y_scaled\\\":0.9473684211},{\\\"ds\\\":\\\"2020-01-20T00:00:00.000Z\\\",\\\"y\\\":19,\\\"floor\\\":0,\\\"t\\\":1.0,\\\"y_scaled\\\":1.0}]}\", \"train_component_cols\": \"{\\\"schema\\\":{\\\"fields\\\":[{\\\"name\\\":\\\"additive_terms\\\",\\\"type\\\":\\\"integer\\\"},{\\\"name\\\":\\\"weekly\\\",\\\"type\\\":\\\"integer\\\"},{\\\"name\\\":\\\"multiplicative_terms\\\",\\\"type\\\":\\\"integer\\\"}],\\\"pandas_version\\\":\\\"1.4.0\\\"},\\\"data\\\":[{\\\"additive_terms\\\":1,\\\"weekly\\\":1,\\\"multiplicative_terms\\\":0},{\\\"additive_terms\\\":1,\\\"weekly\\\":1,\\\"multiplicative_terms\\\":0},{\\\"additive_terms\\\":1,\\\"weekly\\\":1,\\\"multiplicative_terms\\\":0},{\\\"additive_terms\\\":1,\\\"weekly\\\":1,\\\"multiplicative_terms\\\":0},{\\\"additive_terms\\\":1,\\\"weekly\\\":1,\\\"multiplicative_terms\\\":0},{\\\"additive_terms\\\":1,\\\"weekly\\\":1,\\\"multiplicative_terms\\\":0}]}\", \"changepoints_t\": [0.05263157894736842, 0.10526315789473684, 0.15789473684210525, 0.21052631578947367, 0.2631578947368421, 0.3157894736842105, 0.3684210526315789, 0.42105263157894735, 0.47368421052631576, 0.5263157894736842, 0.5789473684210527, 0.631578947368421, 0.6842105263157895, 0.7368421052631579, 0.7894736842105263], \"seasonalities\": [[\"weekly\"], {\"weekly\": {\"period\": 7, \"fourier_order\": 3, \"prior_scale\": 10.0, \"mode\": \"additive\", \"condition_name\": null}}], \"extra_regressors\": [[], {}], \"fit_kwargs\": {}, \"params\": {\"k\": [[1.02697210905145]], \"m\": [[-0.0030769618763556213]], \"delta\": [[-0.0067713344295760635, -1.8489743725556455e-13, -3.3570135589463447e-14, 2.0851492724135376e-13, -0.05560834408005808, -0.03696006862215325, 0.09933974713183089, -0.006771334429730693, 7.87431718983493e-14, -1.234385828898371e-13, 9.554119248353166e-14, -0.05560834408004029, -0.03696006862202002, 0.09933974713167956, -0.006771334429674014]], \"sigma_obs\": [[3.114271923280071e-13]], \"beta\": [[-0.00034958928816783473, 0.0022663968006279714, -0.0009616626944811777, -0.00018603166834996976, -7.034133451705964e-05, -0.00042298794284303893]], \"trend\": [[-0.0030769618763556213, 0.05097420175793122, 0.10466897936960881, 0.15836375698127664, 0.21205853459294272, 0.2657533122046198, 0.3165213348647148, 0.3653440907552229, 0.4193952543895115, 0.4730900320011826, 0.526784809612858, 0.580479587224527, 0.6341743648362007, 0.6849423874962937, 0.7337651433868063, 0.7878163070210918, 0.8415110846327628, 0.8952058622444338, 0.9489006398561047, 1.0025954174677758]]}, \"__prophet_version\": \"1.0.1\"}"