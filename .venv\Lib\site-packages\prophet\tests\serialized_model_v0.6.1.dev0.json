"{\"growth\": \"linear\", \"n_changepoints\": 15, \"specified_changepoints\": false, \"changepoint_range\": 0.8, \"yearly_seasonality\": \"auto\", \"weekly_seasonality\": \"auto\", \"daily_seasonality\": \"auto\", \"seasonality_mode\": \"additive\", \"seasonality_prior_scale\": 10.0, \"changepoint_prior_scale\": 0.05, \"holidays_prior_scale\": 10.0, \"mcmc_samples\": 0, \"interval_width\": 0.8, \"uncertainty_samples\": 1000, \"y_scale\": 19.0, \"logistic_floor\": false, \"country_holidays\": null, \"component_modes\": {\"additive\": [\"weekly\", \"additive_terms\", \"extra_regressors_additive\", \"holidays\"], \"multiplicative\": [\"multiplicative_terms\", \"extra_regressors_multiplicative\"]}, \"fit_kwargs\": {}, \"changepoints\": \"{\\\"name\\\":\\\"ds\\\",\\\"index\\\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15],\\\"data\\\":[\\\"2020-01-02T00:00:00.000Z\\\",\\\"2020-01-03T00:00:00.000Z\\\",\\\"2020-01-04T00:00:00.000Z\\\",\\\"2020-01-05T00:00:00.000Z\\\",\\\"2020-01-06T00:00:00.000Z\\\",\\\"2020-01-07T00:00:00.000Z\\\",\\\"2020-01-08T00:00:00.000Z\\\",\\\"2020-01-09T00:00:00.000Z\\\",\\\"2020-01-10T00:00:00.000Z\\\",\\\"2020-01-11T00:00:00.000Z\\\",\\\"2020-01-12T00:00:00.000Z\\\",\\\"2020-01-13T00:00:00.000Z\\\",\\\"2020-01-14T00:00:00.000Z\\\",\\\"2020-01-15T00:00:00.000Z\\\",\\\"2020-01-16T00:00:00.000Z\\\"]}\", \"history_dates\": \"{\\\"name\\\":\\\"ds\\\",\\\"index\\\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19],\\\"data\\\":[\\\"2020-01-01T00:00:00.000Z\\\",\\\"2020-01-02T00:00:00.000Z\\\",\\\"2020-01-03T00:00:00.000Z\\\",\\\"2020-01-04T00:00:00.000Z\\\",\\\"2020-01-05T00:00:00.000Z\\\",\\\"2020-01-06T00:00:00.000Z\\\",\\\"2020-01-07T00:00:00.000Z\\\",\\\"2020-01-08T00:00:00.000Z\\\",\\\"2020-01-09T00:00:00.000Z\\\",\\\"2020-01-10T00:00:00.000Z\\\",\\\"2020-01-11T00:00:00.000Z\\\",\\\"2020-01-12T00:00:00.000Z\\\",\\\"2020-01-13T00:00:00.000Z\\\",\\\"2020-01-14T00:00:00.000Z\\\",\\\"2020-01-15T00:00:00.000Z\\\",\\\"2020-01-16T00:00:00.000Z\\\",\\\"2020-01-17T00:00:00.000Z\\\",\\\"2020-01-18T00:00:00.000Z\\\",\\\"2020-01-19T00:00:00.000Z\\\",\\\"2020-01-20T00:00:00.000Z\\\"]}\", \"train_holiday_names\": null, \"start\": 1577836800.0, \"t_scale\": 1641600.0, \"holidays\": null, \"history\": \"{\\\"schema\\\":{\\\"fields\\\":[{\\\"name\\\":\\\"ds\\\",\\\"type\\\":\\\"string\\\"},{\\\"name\\\":\\\"y\\\",\\\"type\\\":\\\"integer\\\"},{\\\"name\\\":\\\"floor\\\",\\\"type\\\":\\\"integer\\\"},{\\\"name\\\":\\\"t\\\",\\\"type\\\":\\\"number\\\"},{\\\"name\\\":\\\"y_scaled\\\",\\\"type\\\":\\\"number\\\"}],\\\"pandas_version\\\":\\\"0.20.0\\\"},\\\"data\\\":[{\\\"ds\\\":\\\"2020-01-01T00:00:00.000Z\\\",\\\"y\\\":0,\\\"floor\\\":0,\\\"t\\\":0.0,\\\"y_scaled\\\":0.0},{\\\"ds\\\":\\\"2020-01-02T00:00:00.000Z\\\",\\\"y\\\":1,\\\"floor\\\":0,\\\"t\\\":0.0526315789,\\\"y_scaled\\\":0.0526315789},{\\\"ds\\\":\\\"2020-01-03T00:00:00.000Z\\\",\\\"y\\\":2,\\\"floor\\\":0,\\\"t\\\":0.1052631579,\\\"y_scaled\\\":0.1052631579},{\\\"ds\\\":\\\"2020-01-04T00:00:00.000Z\\\",\\\"y\\\":3,\\\"floor\\\":0,\\\"t\\\":0.1578947368,\\\"y_scaled\\\":0.1578947368},{\\\"ds\\\":\\\"2020-01-05T00:00:00.000Z\\\",\\\"y\\\":4,\\\"floor\\\":0,\\\"t\\\":0.2105263158,\\\"y_scaled\\\":0.2105263158},{\\\"ds\\\":\\\"2020-01-06T00:00:00.000Z\\\",\\\"y\\\":5,\\\"floor\\\":0,\\\"t\\\":0.2631578947,\\\"y_scaled\\\":0.2631578947},{\\\"ds\\\":\\\"2020-01-07T00:00:00.000Z\\\",\\\"y\\\":6,\\\"floor\\\":0,\\\"t\\\":0.3157894737,\\\"y_scaled\\\":0.3157894737},{\\\"ds\\\":\\\"2020-01-08T00:00:00.000Z\\\",\\\"y\\\":7,\\\"floor\\\":0,\\\"t\\\":0.3684210526,\\\"y_scaled\\\":0.3684210526},{\\\"ds\\\":\\\"2020-01-09T00:00:00.000Z\\\",\\\"y\\\":8,\\\"floor\\\":0,\\\"t\\\":0.4210526316,\\\"y_scaled\\\":0.4210526316},{\\\"ds\\\":\\\"2020-01-10T00:00:00.000Z\\\",\\\"y\\\":9,\\\"floor\\\":0,\\\"t\\\":0.4736842105,\\\"y_scaled\\\":0.4736842105},{\\\"ds\\\":\\\"2020-01-11T00:00:00.000Z\\\",\\\"y\\\":10,\\\"floor\\\":0,\\\"t\\\":0.5263157895,\\\"y_scaled\\\":0.5263157895},{\\\"ds\\\":\\\"2020-01-12T00:00:00.000Z\\\",\\\"y\\\":11,\\\"floor\\\":0,\\\"t\\\":0.5789473684,\\\"y_scaled\\\":0.5789473684},{\\\"ds\\\":\\\"2020-01-13T00:00:00.000Z\\\",\\\"y\\\":12,\\\"floor\\\":0,\\\"t\\\":0.6315789474,\\\"y_scaled\\\":0.6315789474},{\\\"ds\\\":\\\"2020-01-14T00:00:00.000Z\\\",\\\"y\\\":13,\\\"floor\\\":0,\\\"t\\\":0.6842105263,\\\"y_scaled\\\":0.6842105263},{\\\"ds\\\":\\\"2020-01-15T00:00:00.000Z\\\",\\\"y\\\":14,\\\"floor\\\":0,\\\"t\\\":0.7368421053,\\\"y_scaled\\\":0.7368421053},{\\\"ds\\\":\\\"2020-01-16T00:00:00.000Z\\\",\\\"y\\\":15,\\\"floor\\\":0,\\\"t\\\":0.7894736842,\\\"y_scaled\\\":0.7894736842},{\\\"ds\\\":\\\"2020-01-17T00:00:00.000Z\\\",\\\"y\\\":16,\\\"floor\\\":0,\\\"t\\\":0.8421052632,\\\"y_scaled\\\":0.8421052632},{\\\"ds\\\":\\\"2020-01-18T00:00:00.000Z\\\",\\\"y\\\":17,\\\"floor\\\":0,\\\"t\\\":0.8947368421,\\\"y_scaled\\\":0.8947368421},{\\\"ds\\\":\\\"2020-01-19T00:00:00.000Z\\\",\\\"y\\\":18,\\\"floor\\\":0,\\\"t\\\":0.9473684211,\\\"y_scaled\\\":0.9473684211},{\\\"ds\\\":\\\"2020-01-20T00:00:00.000Z\\\",\\\"y\\\":19,\\\"floor\\\":0,\\\"t\\\":1.0,\\\"y_scaled\\\":1.0}]}\", \"train_component_cols\": \"{\\\"schema\\\":{\\\"fields\\\":[{\\\"name\\\":\\\"additive_terms\\\",\\\"type\\\":\\\"integer\\\"},{\\\"name\\\":\\\"weekly\\\",\\\"type\\\":\\\"integer\\\"},{\\\"name\\\":\\\"multiplicative_terms\\\",\\\"type\\\":\\\"integer\\\"}],\\\"pandas_version\\\":\\\"0.20.0\\\"},\\\"data\\\":[{\\\"additive_terms\\\":1,\\\"weekly\\\":1,\\\"multiplicative_terms\\\":0},{\\\"additive_terms\\\":1,\\\"weekly\\\":1,\\\"multiplicative_terms\\\":0},{\\\"additive_terms\\\":1,\\\"weekly\\\":1,\\\"multiplicative_terms\\\":0},{\\\"additive_terms\\\":1,\\\"weekly\\\":1,\\\"multiplicative_terms\\\":0},{\\\"additive_terms\\\":1,\\\"weekly\\\":1,\\\"multiplicative_terms\\\":0},{\\\"additive_terms\\\":1,\\\"weekly\\\":1,\\\"multiplicative_terms\\\":0}]}\", \"changepoints_t\": [0.05263157894736842, 0.10526315789473684, 0.15789473684210525, 0.21052631578947367, 0.2631578947368421, 0.3157894736842105, 0.3684210526315789, 0.42105263157894735, 0.47368421052631576, 0.5263157894736842, 0.5789473684210527, 0.631578947368421, 0.6842105263157895, 0.7368421052631579, 0.7894736842105263], \"seasonalities\": [[\"weekly\"], {\"weekly\": {\"period\": 7, \"fourier_order\": 3, \"prior_scale\": 10.0, \"mode\": \"additive\", \"condition_name\": null}}], \"extra_regressors\": [[], {}], \"params\": {\"k\": [[1.0602577093594823]], \"m\": [[-0.004702319079611993]], \"delta\": [[-0.0340439667236795, -8.209452442966747e-14, -2.3754012547042104e-13, 3.543693429450493e-13, -0.1293439581042728, 0.04114775103469311, 0.12224017379322774, -0.03404396672382336, 2.980940404592971e-14, -1.6422578408979347e-13, 3.641514165715795e-13, -0.12934395810441496, 0.0411477510348335, 0.12224017379301462, -0.03404396672364116]], \"sigma_obs\": [[9.77385680687189e-13]], \"beta\": [[-0.0015889307327346812, 0.0029864727119731007, -0.0012450988159614497, -0.0011006619700103823, 0.00041622470940299385, -0.00035495004969666316]]}, \"__fbprophet_version\": \"0.6.1.dev0\"}"