{"version": 3, "file": "1650.43e49e4c78755f921679.js?v=43e49e4c78755f921679", "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AACoE;AACL;AACiD;AACrE;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,8EAAmB,EAAE,yEAAc;AAClD,cAAc,yEAAqB;AACnC;AACA;AACA,wBAAwB,6DAAU;AAClC;AACA,+GAA+G,oFAAyB;AACxI;AACA;AACA;AACA,mCAAmC,qDAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,0DAAO;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,gCAAgC,yDAAM;AACtC;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,KAAK;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,iEAAe,OAAO,EAAC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/docmanager-extension/lib/index.js"], "sourcesContent": ["// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nimport { PageConfig, PathExt, URLExt } from '@jupyterlab/coreutils';\nimport { IDocumentWidgetOpener } from '@jupyterlab/docmanager';\nimport { INotebookPathOpener, INotebookShell, defaultNotebookPathOpener, } from '@jupyter-notebook/application';\nimport { Signal } from '@lumino/signaling';\n/**\n * A plugin to open documents in a new browser tab.\n *\n */\nconst opener = {\n    id: '@jupyter-notebook/docmanager-extension:opener',\n    autoStart: true,\n    optional: [INotebookPathOpener, INotebookShell],\n    provides: IDocumentWidgetOpener,\n    description: 'Open documents in a new browser tab',\n    activate: (app, notebookPathOpener, notebookShell) => {\n        const baseUrl = PageConfig.getBaseUrl();\n        const docRegistry = app.docRegistry;\n        const pathOpener = notebookPathOpener !== null && notebookPathOpener !== void 0 ? notebookPathOpener : defaultNotebookPathOpener;\n        let id = 0;\n        return new (class {\n            constructor() {\n                this._opened = new Signal(this);\n            }\n            async open(widget, options) {\n                var _a, _b, _c;\n                const widgetName = (_a = options === null || options === void 0 ? void 0 : options.type) !== null && _a !== void 0 ? _a : '';\n                const ref = options === null || options === void 0 ? void 0 : options.ref;\n                // check if there is an setting override and if it would add the widget in the main area\n                const userLayoutArea = (_c = (_b = notebookShell === null || notebookShell === void 0 ? void 0 : notebookShell.userLayout) === null || _b === void 0 ? void 0 : _b[widgetName]) === null || _c === void 0 ? void 0 : _c.area;\n                if (ref !== '_noref' && userLayoutArea === undefined) {\n                    const path = widget.context.path;\n                    const ext = PathExt.extname(path);\n                    let route = 'edit';\n                    if ((widgetName === 'default' && ext === '.ipynb') ||\n                        widgetName.includes('Notebook')) {\n                        // make sure to save the notebook before opening it in a new tab\n                        // so the kernel info is saved (if created from the New dropdown)\n                        if (widget.context.sessionContext.kernelPreference.name) {\n                            await widget.context.save();\n                        }\n                        route = 'notebooks';\n                    }\n                    // append ?factory only if it's not the default\n                    const defaultFactory = docRegistry.defaultWidgetFactory(path);\n                    let searchParams = undefined;\n                    if (widgetName !== defaultFactory.name) {\n                        searchParams = new URLSearchParams({\n                            factory: widgetName,\n                        });\n                    }\n                    pathOpener.open({\n                        prefix: URLExt.join(baseUrl, route),\n                        path,\n                        searchParams,\n                    });\n                    // dispose the widget since it is not used on this page\n                    widget.dispose();\n                    return;\n                }\n                // otherwise open the document on the current page\n                if (!widget.id) {\n                    widget.id = `document-manager-${++id}`;\n                }\n                widget.title.dataset = {\n                    type: 'document-title',\n                    ...widget.title.dataset,\n                };\n                if (!widget.isAttached) {\n                    app.shell.add(widget, 'main', options || {});\n                }\n                app.shell.activateById(widget.id);\n                this._opened.emit(widget);\n            }\n            get opened() {\n                return this._opened;\n            }\n        })();\n    },\n};\n/**\n * Export the plugins as default.\n */\nconst plugins = [opener];\nexport default plugins;\n"], "names": [], "sourceRoot": ""}