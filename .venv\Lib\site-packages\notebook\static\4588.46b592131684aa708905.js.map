{"version": 3, "file": "4588.46b592131684aa708905.js?v=46b592131684aa708905", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAwE;AACrB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;;AAEA;AACA;AACA;AACA;AACA,kBAAkB,iBAAiB;AACnC;;AAEA,2BAA2B,2BAAc;AACzC;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH,kBAAkB,mCAAmC;AACrD;AACA,CAAC;;AAED,qBAAqB,8BAAiB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,IAAI;AACrC;AACA,IAAI;AACJ;AACA;AACA,CAAC,GAAG,iBAAiB;;AAErB;AACA,aAAa,8BAAiB;AAC9B,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;;AAEA,wBAAwB,uBAAS;AACjC,QAAQ,cAAI;AACZ,mDAAmD,cAAI;AACvD,WAAW,cAAI;AACf,iCAAiC,cAAI,UAAU,cAAI;AACnD,iBAAiB,cAAI;AACrB,kBAAkB,cAAI;AACtB,MAAM,cAAI;AACV,wCAAwC,cAAI;AAC5C,WAAW,cAAI;AACf,kBAAkB,cAAI;AACtB,eAAe,cAAI;AACnB,SAAS,cAAI,SAAS,cAAI;AAC1B,CAAC;;AAED;AACA,eAAe,qBAAQ;AACvB;AACA,8DAA8D,oIAAoI,IAAI,0EAA0E,IAAI,oPAAoP,IAAI,0DAA0D,gKAAgK,IAAI,0IAA0I,4BAA4B,IAAI;AACp5B,2CAA2C,iDAAiD,iFAAiF,WAAW,uNAAuN,2BAA2B;AAC1a,+CAA+C,2IAA2I;AAC1L;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,GAAG,gBAAgB,oDAAoD,IAAI,2JAA2J,KAAK,GAAG,KAAK,GAAG,iDAAiD,2BAA2B,KAAK,GAAG,4BAA4B,KAAK,GAAG,eAAe,YAAY,WAAW,KAAK,GAAG,eAAe,YAAY,cAAc,yDAAyD,KAAK,GAAG,8BAA8B,KAAK,GAAG,kCAAkC,KAAK,GAAG,oCAAoC,KAAK,GAAG,qBAAqB,uBAAuB,KAAK,GAAG,aAAa,mBAAmB,KAAK,GAAG,qBAAqB,cAAc,eAAe,GAAG,mBAAmB,KAAK,GAAG,iBAAiB,uCAAuC,KAAK,GAAG,mBAAmB,2BAA2B,KAAK,GAAG,iBAAiB,uCAAuC,KAAK,GAAG,aAAa,MAAM,2BAA2B,KAAK,GAAG,sBAAsB,eAAe,GAAG,kDAAkD,IAAI,0BAA0B,IAAI,6IAA6I,KAAK,GAAG,KAAK,GAAG,+CAA+C,UAAU,EAAE,2BAA2B,KAAK,GAAG,eAAe,WAAW,GAAG,oBAAoB,IAAI,mBAAmB,KAAK,GAAG,eAAe,WAAW,GAAG,IAAI,GAAG,KAAK,GAAG,eAAe,EAAE,WAAW,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,eAAe,YAAY,IAAI,OAAO,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,eAAe,+BAA+B,KAAK,GAAG,yBAAyB,GAAG,KAAK,GAAG,qBAAqB,cAAc,WAAW,KAAK,GAAG,eAAe,YAAY,mCAAmC,KAAK,GAAG,eAAe,mBAAmB,IAAI,KAAK,GAAG,YAAY,GAAG,mBAAmB,IAAI,KAAK,GAAG,YAAY,GAAG,uBAAuB,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,eAAe,YAAY,IAAI,OAAO,KAAK,GAAG,eAAe,aAAa,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,eAAe,uBAAuB,KAAK,GAAG,aAAa,EAAE,uBAAuB,KAAK,GAAG,kBAAkB,WAAW,KAAK,GAAG,mBAAmB,2BAA2B,KAAK,GAAG,mBAAmB,2BAA2B,KAAK,GAAG,iBAAiB,uCAAuC,KAAK,GAAG,oBAAoB,2BAA2B,KAAK,GAAG,iBAAiB,uCAAuC,KAAK,GAAG,iBAAiB,uCAAuC,KAAK,GAAG,mBAAmB,2BAA2B,KAAK,GAAG;AACjvG;AACA,aAAa,iBAAiB;AAC9B;AACA,CAAC;;AAEiB;;;;;;;;;AC9IkB;AACgG;AAChF;AACN;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,KAAK;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,6BAAU;AACvB,uBAAuB,sBAAsB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB,oBAAoB;AACpB;AACA,qBAAqB;AACrB;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA,qBAAqB;AACrB;AACA,qBAAqB;AACrB;AACA,qBAAqB;AACrB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,MAAM,YAAO;AACb;AACA;AACA;AACA;AACA;AACA,wDAAwD,cAAc,uBAAuB,KAAK,kBAAkB;AACpH,4DAA4D,sBAAsB,wBAAwB;AAC1G,6DAA6D,sBAAsB,yCAAyC;AAC5H,iEAAiE,sBAAsB,wBAAwB;AAC/G,oEAAoE,wBAAwB;AAC5F;AACA;AACA;AACA;AACA,yCAAyC,kBAAkB,uBAAuB,KAAK,kBAAkB;AACzG;AACA;AACA,uCAAuC,WAAW,KAAK;AACvD;AACA,4CAA4C,WAAW,WAAW,WAAW,IAAI;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE;AACjE;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,sBAAsB,YAAO;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,wBAAwB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,MAAM;AACpB;AACA;AACA,cAAc,sBAAsB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uHAAuH,uCAAuC;AAC9J;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE,wDAAwD;AACzH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iCAAiC,yBAAU;AAC3C;AACA,yBAAyB,MAAM;AAC/B;AACA,yBAAyB,6BAAc;AACvC;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,yBAAyB,2BAAY;AACrC;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,aAAa;AACb,yBAAyB,oCAAqB;AAC9C;AACA,aAAa;AACb;AACA,KAAK;AACL;AACA,yBAAyB,SAAS,8BAA8B;AAChE;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,SAAS;AACT;AACA;AACA,eAAe,8BAAe;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,8BAAU;AAC7C;AACA;AACA;AACA,sCAAsC,QAAQ;AAC9C;AACA;AACA,cAAc,OAAO;AACrB;AACA,oBAAoB,6BAAU;AAC9B;AACA;AACA;AACA;AACA;AACA,kCAAkC,KAAK;AACvC,yBAAyB,kBAAkB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,KAAK;AACrC;AACA,2BAA2B,oCAAe;AAC1C,+BAA+B;AAC/B;AACA;AACA;AACA,iBAAiB;AACjB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,CAAC;;AAE8D", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@lezer/xml/dist/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/lang-xml/dist/index.js"], "sourcesContent": ["import { ContextTracker, ExternalTokenizer, LRParser } from '@lezer/lr';\nimport { styleTags, tags } from '@lezer/highlight';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst StartTag = 1,\n  StartCloseTag = 2,\n  MissingCloseTag = 3,\n  mismatchedStartCloseTag = 4,\n  incompleteStartCloseTag = 5,\n  commentContent$1 = 35,\n  piContent$1 = 36,\n  cdataContent$1 = 37,\n  Element = 11,\n  OpenTag = 13;\n\n/* Hand-written tokenizer for XML tag matching. */\n\nfunction nameChar(ch) {\n  return ch == 45 || ch == 46 || ch == 58 || ch >= 65 && ch <= 90 || ch == 95 || ch >= 97 && ch <= 122 || ch >= 161\n}\n\nfunction isSpace(ch) {\n  return ch == 9 || ch == 10 || ch == 13 || ch == 32\n}\n\nlet cachedName = null, cachedInput = null, cachedPos = 0;\nfunction tagNameAfter(input, offset) {\n  let pos = input.pos + offset;\n  if (cachedInput == input && cachedPos == pos) return cachedName\n  while (isSpace(input.peek(offset))) offset++;\n  let name = \"\";\n  for (;;) {\n    let next = input.peek(offset);\n    if (!nameChar(next)) break\n    name += String.fromCharCode(next);\n    offset++;\n  }\n  cachedInput = input; cachedPos = pos;\n  return cachedName = name || null\n}\n\nfunction ElementContext(name, parent) {\n  this.name = name;\n  this.parent = parent;\n  this.hash = parent ? parent.hash : 0;\n  for (let i = 0; i < name.length; i++) this.hash += (this.hash << 4) + name.charCodeAt(i) + (name.charCodeAt(i) << 8);\n}\n\nconst elementContext = new ContextTracker({\n  start: null,\n  shift(context, term, stack, input) {\n    return term == StartTag ? new ElementContext(tagNameAfter(input, 1) || \"\", context) : context\n  },\n  reduce(context, term) {\n    return term == Element && context ? context.parent : context\n  },\n  reuse(context, node, _stack, input) {\n    let type = node.type.id;\n    return type == StartTag || type == OpenTag\n      ? new ElementContext(tagNameAfter(input, 1) || \"\", context) : context\n  },\n  hash(context) { return context ? context.hash : 0 },\n  strict: false\n});\n\nconst startTag = new ExternalTokenizer((input, stack) => {\n  if (input.next != 60 /* '<' */) return\n  input.advance();\n  if (input.next == 47 /* '/' */) {\n    input.advance();\n    let name = tagNameAfter(input, 0);\n    if (!name) return input.acceptToken(incompleteStartCloseTag)\n    if (stack.context && name == stack.context.name) return input.acceptToken(StartCloseTag)\n    for (let cx = stack.context; cx; cx = cx.parent) if (cx.name == name) return input.acceptToken(MissingCloseTag, -2)\n    input.acceptToken(mismatchedStartCloseTag);\n  } else if (input.next != 33 /* '!' */ && input.next != 63 /* '?' */) {\n    return input.acceptToken(StartTag)\n  }\n}, {contextual: true});\n\nfunction scanTo(type, end) {\n  return new ExternalTokenizer(input => {\n    for (let endPos = 0, len = 0;; len++) {\n      if (input.next < 0) {\n        if (len) input.acceptToken(type);\n        break\n      } \n      if (input.next == end.charCodeAt(endPos)) {\n        endPos++;\n        if (endPos == end.length) {\n          if (len >= end.length) input.acceptToken(type, 1 - end.length);\n          break\n        }\n      } else {\n        endPos = input.next == end.charCodeAt(0) ? 1 : 0;\n      }\n      input.advance();\n    }\n  })\n}\n\nconst commentContent = scanTo(commentContent$1, \"-->\");\nconst piContent = scanTo(piContent$1, \"?>\");\nconst cdataContent = scanTo(cdataContent$1, \"]]>\");\n\nconst xmlHighlighting = styleTags({\n  Text: tags.content,\n  \"StartTag StartCloseTag EndTag SelfCloseEndTag\": tags.angleBracket,\n  TagName: tags.tagName,\n  \"MismatchedCloseTag/Tagname\": [tags.tagName, tags.invalid],\n  AttributeName: tags.attributeName,\n  AttributeValue: tags.attributeValue,\n  Is: tags.definitionOperator,\n  \"EntityReference CharacterReference\": tags.character,\n  Comment: tags.blockComment,\n  ProcessingInst: tags.processingInstruction,\n  DoctypeDecl: tags.documentMeta,\n  Cdata: tags.special(tags.string)\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \",SOQOaOOOrOxO'#CfOzOpO'#CiO!tOaO'#CgOOOP'#Cg'#CgO!{OrO'#CrO#TOtO'#CsO#]OpO'#CtOOOP'#DS'#DSOOOP'#Cv'#CvQQOaOOOOOW'#Cw'#CwO#eOxO,59QOOOP,59Q,59QOOOO'#Cx'#CxO#mOpO,59TO#uO!bO,59TOOOP'#C{'#C{O$TOaO,59RO$[OpO'#CoOOOP,59R,59ROOOQ'#C|'#C|O$dOrO,59^OOOP,59^,59^OOOS'#C}'#C}O$lOtO,59_OOOP,59_,59_O$tOpO,59`O$|OpO,59`OOOP-E6t-E6tOOOW-E6u-E6uOOOP1G.l1G.lOOOO-E6v-E6vO%UO!bO1G.oO%UO!bO1G.oO%dOpO'#CkO%lO!bO'#CyO%zO!bO1G.oOOOP1G.o1G.oOOOP1G.w1G.wOOOP-E6y-E6yOOOP1G.m1G.mO&VOpO,59ZO&_OpO,59ZOOOQ-E6z-E6zOOOP1G.x1G.xOOOS-E6{-E6{OOOP1G.y1G.yO&gOpO1G.zO&gOpO1G.zOOOP1G.z1G.zO&oO!bO7+$ZO&}O!bO7+$ZOOOP7+$Z7+$ZOOOP7+$c7+$cO'YOpO,59VO'bOpO,59VO'jO!bO,59eOOOO-E6w-E6wO'xOpO1G.uO'xOpO1G.uOOOP1G.u1G.uO(QOpO7+$fOOOP7+$f7+$fO(YO!bO<<GuOOOP<<Gu<<GuOOOP<<G}<<G}O'bOpO1G.qO'bOpO1G.qO(eO#tO'#CnOOOO1G.q1G.qO(sOpO7+$aOOOP7+$a7+$aOOOP<<HQ<<HQOOOPAN=aAN=aOOOPAN=iAN=iO'bOpO7+$]OOOO7+$]7+$]OOOO'#Cz'#CzO({O#tO,59YOOOO,59Y,59YOOOP<<G{<<G{OOOO<<Gw<<GwOOOO-E6x-E6xOOOO1G.t1G.t\",\n  stateData: \")Z~OPQOSVOTWOVWOWWOXWOiXOxPO}TO!PUO~OuZOw]O~O^`Oy^O~OPQOQcOSVOTWOVWOWWOXWOxPO}TO!PUO~ORdO~P!SOseO|gO~OthO!OjO~O^lOy^O~OuZOwoO~O^qOy^O~O[vO`sOdwOy^O~ORyO~P!SO^{Oy^O~OseO|}O~OthO!O!PO~O^!QOy^O~O[!SOy^O~O[!VO`sOd!WOy^O~Oa!YOy^O~Oy^O[mX`mXdmX~O[!VO`sOd!WO~O^!]Oy^O~O[!_Oy^O~O[!aOy^O~O[!cO`sOd!dOy^O~O[!cO`sOd!dO~Oa!eOy^O~Oy^Oz!gO~Oy^O[ma`madma~O[!jOy^O~O[!kOy^O~O[!lO`sOd!mO~OW!pOX!pOz!rO{!pO~O[!sOy^O~OW!pOX!pOz!vO{!pO~O\",\n  goto: \"%[wPPPPPPPPPPxxP!OP!UPP!_!iP!oxxxP!u!{#R$Z$j$p$v$|PPPP%SXWORYbXRORYb_t`qru!T!U!bQ!h!YS!o!e!fR!t!nQdRRybXSORYbQYORmYQ[PRn[Q_QQkVjp_krz!R!T!X!Z!^!`!f!i!nQr`QzcQ!RlQ!TqQ!XsQ!ZtQ!^{Q!`!QQ!f!YQ!i!]R!n!eQu`S!UqrU![u!U!bR!b!TQ!q!gR!u!qQbRRxbQfTR|fQiUR!OiSXOYTaRb\",\n  nodeNames: \"⚠ StartTag StartCloseTag MissingCloseTag StartCloseTag StartCloseTag Document Text EntityReference CharacterReference Cdata Element EndTag OpenTag TagName Attribute AttributeName Is AttributeValue CloseTag SelfCloseEndTag SelfClosingTag Comment ProcessingInst MismatchedCloseTag DoctypeDecl\",\n  maxTerm: 47,\n  context: elementContext,\n  nodeProps: [\n    [\"closedBy\", 1,\"SelfCloseEndTag EndTag\",13,\"CloseTag MissingCloseTag\"],\n    [\"openedBy\", 12,\"StartTag StartCloseTag\",19,\"OpenTag\",20,\"StartTag\"]\n  ],\n  propSources: [xmlHighlighting],\n  skippedNodes: [0],\n  repeatNodeCount: 8,\n  tokenData: \"IX~R!XOX$nXY&kYZ&kZ]$n]^&k^p$npq&kqr$nrs'ssv$nvw(Zw}$n}!O*l!O!P$n!P!Q,{!Q![$n![!].e!]!^$n!^!_1v!_!`Cz!`!aDm!a!bE`!b!c$n!c!}.e!}#P$n#P#QFx#Q#R$n#R#S.e#S#T$n#T#o.e#o%W$n%W%o.e%o%p$n%p&a.e&a&b$n&b1p.e1p4U$n4U4d.e4d4e$n4e$IS.e$IS$I`$n$I`$Ib.e$Ib$Kh$n$Kh%#t.e%#t&/x$n&/x&Et.e&Et&FV$n&FV;'S.e;'S;:j1p;:j;=`&e<%l?&r$n?&r?Ah.e?Ah?BY$n?BY?Mn.e?MnO$nX$uWVP{WOr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$nP%dTVPOv%_w!^%_!_;'S%_;'S;=`%s<%lO%_P%vP;=`<%l%_W&OT{WOr%ysv%yw;'S%y;'S;=`&_<%lO%yW&bP;=`<%l%yX&hP;=`<%l$n_&t_VP{WyUOX$nXY&kYZ&kZ]$n]^&k^p$npq&kqr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$nZ'zTzYVPOv%_w!^%_!_;'S%_;'S;=`%s<%lO%_~(^VOp(sqs(sst)ht!](s!^;'S(s;'S;=`)b<%lO(s~(vVOp(sqs(st!](s!]!^)]!^;'S(s;'S;=`)b<%lO(s~)bOW~~)eP;=`<%l(s~)kTOp)zq!])z!^;'S)z;'S;=`*f<%lO)z~)}UOp)zq!])z!]!^*a!^;'S)z;'S;=`*f<%lO)z~*fOX~~*iP;=`<%l)zZ*sYVP{WOr$nrs%_sv$nw}$n}!O+c!O!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$nZ+jYVP{WOr$nrs%_sv$nw!^$n!^!_%y!_!`$n!`!a,Y!a;'S$n;'S;=`&e<%lO$nZ,cW|QVP{WOr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$n]-SYVP{WOr$nrs%_sv$nw!^$n!^!_%y!_!`$n!`!a-r!a;'S$n;'S;=`&e<%lO$n]-{WdSVP{WOr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$n_.p!O`S^QVP{WOr$nrs%_sv$nw}$n}!O.e!O!P.e!P!Q$n!Q![.e![!].e!]!^$n!^!_%y!_!c$n!c!}.e!}#R$n#R#S.e#S#T$n#T#o.e#o$}$n$}%O.e%O%W$n%W%o.e%o%p$n%p&a.e&a&b$n&b1p.e1p4U.e4U4d.e4d4e$n4e$IS.e$IS$I`$n$I`$Ib.e$Ib$Je$n$Je$Jg.e$Jg$Kh$n$Kh%#t.e%#t&/x$n&/x&Et.e&Et&FV$n&FV;'S.e;'S;:j1p;:j;=`&e<%l?&r$n?&r?Ah.e?Ah?BY$n?BY?Mn.e?MnO$n_1sP;=`<%l.eX1{W{WOq%yqr2esv%yw!a%y!a!bCd!b;'S%y;'S;=`&_<%lO%yX2j]{WOr%ysv%yw}%y}!O3c!O!f%y!f!g4e!g!}%y!}#O9t#O#W%y#W#X@Q#X;'S%y;'S;=`&_<%lO%yX3hV{WOr%ysv%yw}%y}!O3}!O;'S%y;'S;=`&_<%lO%yX4UT}P{WOr%ysv%yw;'S%y;'S;=`&_<%lO%yX4jV{WOr%ysv%yw!q%y!q!r5P!r;'S%y;'S;=`&_<%lO%yX5UV{WOr%ysv%yw!e%y!e!f5k!f;'S%y;'S;=`&_<%lO%yX5pV{WOr%ysv%yw!v%y!v!w6V!w;'S%y;'S;=`&_<%lO%yX6[V{WOr%ysv%yw!{%y!{!|6q!|;'S%y;'S;=`&_<%lO%yX6vV{WOr%ysv%yw!r%y!r!s7]!s;'S%y;'S;=`&_<%lO%yX7bV{WOr%ysv%yw!g%y!g!h7w!h;'S%y;'S;=`&_<%lO%yX7|X{WOr7wrs8isv7wvw8iw!`7w!`!a9W!a;'S7w;'S;=`9n<%lO7wP8lTO!`8i!`!a8{!a;'S8i;'S;=`9Q<%lO8iP9QOiPP9TP;=`<%l8iX9_TiP{WOr%ysv%yw;'S%y;'S;=`&_<%lO%yX9qP;=`<%l7wX9yX{WOr%ysv%yw!e%y!e!f:f!f#V%y#V#W=t#W;'S%y;'S;=`&_<%lO%yX:kV{WOr%ysv%yw!f%y!f!g;Q!g;'S%y;'S;=`&_<%lO%yX;VV{WOr%ysv%yw!c%y!c!d;l!d;'S%y;'S;=`&_<%lO%yX;qV{WOr%ysv%yw!v%y!v!w<W!w;'S%y;'S;=`&_<%lO%yX<]V{WOr%ysv%yw!c%y!c!d<r!d;'S%y;'S;=`&_<%lO%yX<wV{WOr%ysv%yw!}%y!}#O=^#O;'S%y;'S;=`&_<%lO%yX=eT{WxPOr%ysv%yw;'S%y;'S;=`&_<%lO%yX=yV{WOr%ysv%yw#W%y#W#X>`#X;'S%y;'S;=`&_<%lO%yX>eV{WOr%ysv%yw#T%y#T#U>z#U;'S%y;'S;=`&_<%lO%yX?PV{WOr%ysv%yw#h%y#h#i?f#i;'S%y;'S;=`&_<%lO%yX?kV{WOr%ysv%yw#T%y#T#U<r#U;'S%y;'S;=`&_<%lO%yX@VV{WOr%ysv%yw#c%y#c#d@l#d;'S%y;'S;=`&_<%lO%yX@qV{WOr%ysv%yw#V%y#V#WAW#W;'S%y;'S;=`&_<%lO%yXA]V{WOr%ysv%yw#h%y#h#iAr#i;'S%y;'S;=`&_<%lO%yXAwV{WOr%ysv%yw#m%y#m#nB^#n;'S%y;'S;=`&_<%lO%yXBcV{WOr%ysv%yw#d%y#d#eBx#e;'S%y;'S;=`&_<%lO%yXB}V{WOr%ysv%yw#X%y#X#Y7w#Y;'S%y;'S;=`&_<%lO%yXCkT!PP{WOr%ysv%yw;'S%y;'S;=`&_<%lO%yZDTWaQVP{WOr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$n_DvW[UVP{WOr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$nZEgYVP{WOr$nrs%_sv$nw!^$n!^!_%y!_!`$n!`!aFV!a;'S$n;'S;=`&e<%lO$nZF`W!OQVP{WOr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$nZGPYVP{WOr$nrs%_sv$nw!^$n!^!_%y!_#P$n#P#QGo#Q;'S$n;'S;=`&e<%lO$nZGvYVP{WOr$nrs%_sv$nw!^$n!^!_%y!_!`$n!`!aHf!a;'S$n;'S;=`&e<%lO$nZHoWwQVP{WOr$nrs%_sv$nw!^$n!^!_%y!_;'S$n;'S;=`&e<%lO$n\",\n  tokenizers: [startTag, commentContent, piContent, cdataContent, 0, 1, 2, 3],\n  topRules: {\"Document\":[0,6]},\n  tokenPrec: 0\n});\n\nexport { parser };\n", "import { parser } from '@lezer/xml';\nimport { syntaxTree, LRLanguage, indentNodeProp, foldNodeProp, bracketMatchingHandle, LanguageSupport } from '@codemirror/language';\nimport { EditorSelection } from '@codemirror/state';\nimport { EditorView } from '@codemirror/view';\n\nfunction tagName(doc, tag) {\n    let name = tag && tag.getChild(\"TagName\");\n    return name ? doc.sliceString(name.from, name.to) : \"\";\n}\nfunction elementName$1(doc, tree) {\n    let tag = tree && tree.firstChild;\n    return !tag || tag.name != \"OpenTag\" ? \"\" : tagName(doc, tag);\n}\nfunction attrName(doc, tag, pos) {\n    let attr = tag && tag.getChildren(\"Attribute\").find(a => a.from <= pos && a.to >= pos);\n    let name = attr && attr.getChild(\"AttributeName\");\n    return name ? doc.sliceString(name.from, name.to) : \"\";\n}\nfunction findParentElement(tree) {\n    for (let cur = tree && tree.parent; cur; cur = cur.parent)\n        if (cur.name == \"Element\")\n            return cur;\n    return null;\n}\nfunction findLocation(state, pos) {\n    var _a;\n    let at = syntaxTree(state).resolveInner(pos, -1), inTag = null;\n    for (let cur = at; !inTag && cur.parent; cur = cur.parent)\n        if (cur.name == \"OpenTag\" || cur.name == \"CloseTag\" || cur.name == \"SelfClosingTag\" || cur.name == \"MismatchedCloseTag\")\n            inTag = cur;\n    if (inTag && (inTag.to > pos || inTag.lastChild.type.isError)) {\n        let elt = inTag.parent;\n        if (at.name == \"TagName\")\n            return inTag.name == \"CloseTag\" || inTag.name == \"MismatchedCloseTag\"\n                ? { type: \"closeTag\", from: at.from, context: elt }\n                : { type: \"openTag\", from: at.from, context: findParentElement(elt) };\n        if (at.name == \"AttributeName\")\n            return { type: \"attrName\", from: at.from, context: inTag };\n        if (at.name == \"AttributeValue\")\n            return { type: \"attrValue\", from: at.from, context: inTag };\n        let before = at == inTag || at.name == \"Attribute\" ? at.childBefore(pos) : at;\n        if ((before === null || before === void 0 ? void 0 : before.name) == \"StartTag\")\n            return { type: \"openTag\", from: pos, context: findParentElement(elt) };\n        if ((before === null || before === void 0 ? void 0 : before.name) == \"StartCloseTag\" && before.to <= pos)\n            return { type: \"closeTag\", from: pos, context: elt };\n        if ((before === null || before === void 0 ? void 0 : before.name) == \"Is\")\n            return { type: \"attrValue\", from: pos, context: inTag };\n        if (before)\n            return { type: \"attrName\", from: pos, context: inTag };\n        return null;\n    }\n    else if (at.name == \"StartCloseTag\") {\n        return { type: \"closeTag\", from: pos, context: at.parent };\n    }\n    while (at.parent && at.to == pos && !((_a = at.lastChild) === null || _a === void 0 ? void 0 : _a.type.isError))\n        at = at.parent;\n    if (at.name == \"Element\" || at.name == \"Text\" || at.name == \"Document\")\n        return { type: \"tag\", from: pos, context: at.name == \"Element\" ? at : findParentElement(at) };\n    return null;\n}\nclass Element {\n    constructor(spec, attrs, attrValues) {\n        this.attrs = attrs;\n        this.attrValues = attrValues;\n        this.children = [];\n        this.name = spec.name;\n        this.completion = Object.assign(Object.assign({ type: \"type\" }, spec.completion || {}), { label: this.name });\n        this.openCompletion = Object.assign(Object.assign({}, this.completion), { label: \"<\" + this.name });\n        this.closeCompletion = Object.assign(Object.assign({}, this.completion), { label: \"</\" + this.name + \">\", boost: 2 });\n        this.closeNameCompletion = Object.assign(Object.assign({}, this.completion), { label: this.name + \">\" });\n        this.text = spec.textContent ? spec.textContent.map(s => ({ label: s, type: \"text\" })) : [];\n    }\n}\nconst Identifier = /^[:\\-\\.\\w\\u00b7-\\uffff]*$/;\nfunction attrCompletion(spec) {\n    return Object.assign(Object.assign({ type: \"property\" }, spec.completion || {}), { label: spec.name });\n}\nfunction valueCompletion(spec) {\n    return typeof spec == \"string\" ? { label: `\"${spec}\"`, type: \"constant\" }\n        : /^\"/.test(spec.label) ? spec\n            : Object.assign(Object.assign({}, spec), { label: `\"${spec.label}\"` });\n}\n/**\nCreate a completion source for the given schema.\n*/\nfunction completeFromSchema(eltSpecs, attrSpecs) {\n    let allAttrs = [], globalAttrs = [];\n    let attrValues = Object.create(null);\n    for (let s of attrSpecs) {\n        let completion = attrCompletion(s);\n        allAttrs.push(completion);\n        if (s.global)\n            globalAttrs.push(completion);\n        if (s.values)\n            attrValues[s.name] = s.values.map(valueCompletion);\n    }\n    let allElements = [], topElements = [];\n    let byName = Object.create(null);\n    for (let s of eltSpecs) {\n        let attrs = globalAttrs, attrVals = attrValues;\n        if (s.attributes)\n            attrs = attrs.concat(s.attributes.map(s => {\n                if (typeof s == \"string\")\n                    return allAttrs.find(a => a.label == s) || { label: s, type: \"property\" };\n                if (s.values) {\n                    if (attrVals == attrValues)\n                        attrVals = Object.create(attrVals);\n                    attrVals[s.name] = s.values.map(valueCompletion);\n                }\n                return attrCompletion(s);\n            }));\n        let elt = new Element(s, attrs, attrVals);\n        byName[elt.name] = elt;\n        allElements.push(elt);\n        if (s.top)\n            topElements.push(elt);\n    }\n    if (!topElements.length)\n        topElements = allElements;\n    for (let i = 0; i < allElements.length; i++) {\n        let s = eltSpecs[i], elt = allElements[i];\n        if (s.children) {\n            for (let ch of s.children)\n                if (byName[ch])\n                    elt.children.push(byName[ch]);\n        }\n        else {\n            elt.children = allElements;\n        }\n    }\n    return cx => {\n        var _a;\n        let { doc } = cx.state, loc = findLocation(cx.state, cx.pos);\n        if (!loc || (loc.type == \"tag\" && !cx.explicit))\n            return null;\n        let { type, from, context } = loc;\n        if (type == \"openTag\") {\n            let children = topElements;\n            let parentName = elementName$1(doc, context);\n            if (parentName) {\n                let parent = byName[parentName];\n                children = (parent === null || parent === void 0 ? void 0 : parent.children) || allElements;\n            }\n            return {\n                from,\n                options: children.map(ch => ch.completion),\n                validFor: Identifier\n            };\n        }\n        else if (type == \"closeTag\") {\n            let parentName = elementName$1(doc, context);\n            return parentName ? {\n                from,\n                to: cx.pos + (doc.sliceString(cx.pos, cx.pos + 1) == \">\" ? 1 : 0),\n                options: [((_a = byName[parentName]) === null || _a === void 0 ? void 0 : _a.closeNameCompletion) || { label: parentName + \">\", type: \"type\" }],\n                validFor: Identifier\n            } : null;\n        }\n        else if (type == \"attrName\") {\n            let parent = byName[tagName(doc, context)];\n            return {\n                from,\n                options: (parent === null || parent === void 0 ? void 0 : parent.attrs) || globalAttrs,\n                validFor: Identifier\n            };\n        }\n        else if (type == \"attrValue\") {\n            let attr = attrName(doc, context, from);\n            if (!attr)\n                return null;\n            let parent = byName[tagName(doc, context)];\n            let values = ((parent === null || parent === void 0 ? void 0 : parent.attrValues) || attrValues)[attr];\n            if (!values || !values.length)\n                return null;\n            return {\n                from,\n                to: cx.pos + (doc.sliceString(cx.pos, cx.pos + 1) == '\"' ? 1 : 0),\n                options: values,\n                validFor: /^\"[^\"]*\"?$/\n            };\n        }\n        else if (type == \"tag\") {\n            let parentName = elementName$1(doc, context), parent = byName[parentName];\n            let closing = [], last = context && context.lastChild;\n            if (parentName && (!last || last.name != \"CloseTag\" || tagName(doc, last) != parentName))\n                closing.push(parent ? parent.closeCompletion : { label: \"</\" + parentName + \">\", type: \"type\", boost: 2 });\n            let options = closing.concat(((parent === null || parent === void 0 ? void 0 : parent.children) || (context ? allElements : topElements)).map(e => e.openCompletion));\n            if (context && (parent === null || parent === void 0 ? void 0 : parent.text.length)) {\n                let openTag = context.firstChild;\n                if (openTag.to > cx.pos - 20 && !/\\S/.test(cx.state.sliceDoc(openTag.to, cx.pos)))\n                    options = options.concat(parent.text);\n            }\n            return {\n                from,\n                options,\n                validFor: /^<\\/?[:\\-\\.\\w\\u00b7-\\uffff]*$/\n            };\n        }\n        else {\n            return null;\n        }\n    };\n}\n\n/**\nA language provider based on the [Lezer XML\nparser](https://github.com/lezer-parser/xml), extended with\nhighlighting and indentation information.\n*/\nconst xmlLanguage = /*@__PURE__*/LRLanguage.define({\n    name: \"xml\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/indentNodeProp.add({\n                Element(context) {\n                    let closed = /^\\s*<\\//.test(context.textAfter);\n                    return context.lineIndent(context.node.from) + (closed ? 0 : context.unit);\n                },\n                \"OpenTag CloseTag SelfClosingTag\"(context) {\n                    return context.column(context.node.from) + context.unit;\n                }\n            }),\n            /*@__PURE__*/foldNodeProp.add({\n                Element(subtree) {\n                    let first = subtree.firstChild, last = subtree.lastChild;\n                    if (!first || first.name != \"OpenTag\")\n                        return null;\n                    return { from: first.to, to: last.name == \"CloseTag\" ? last.from : subtree.to };\n                }\n            }),\n            /*@__PURE__*/bracketMatchingHandle.add({\n                \"OpenTag CloseTag\": node => node.getChild(\"TagName\")\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"<!--\", close: \"-->\" } },\n        indentOnInput: /^\\s*<\\/$/\n    }\n});\n/**\nXML language support. Includes schema-based autocompletion when\nconfigured.\n*/\nfunction xml(conf = {}) {\n    let support = [xmlLanguage.data.of({\n            autocomplete: completeFromSchema(conf.elements || [], conf.attributes || [])\n        })];\n    if (conf.autoCloseTags !== false)\n        support.push(autoCloseTags);\n    return new LanguageSupport(xmlLanguage, support);\n}\nfunction elementName(doc, tree, max = doc.length) {\n    if (!tree)\n        return \"\";\n    let tag = tree.firstChild;\n    let name = tag && tag.getChild(\"TagName\");\n    return name ? doc.sliceString(name.from, Math.min(name.to, max)) : \"\";\n}\n/**\nExtension that will automatically insert close tags when a `>` or\n`/` is typed.\n*/\nconst autoCloseTags = /*@__PURE__*/EditorView.inputHandler.of((view, from, to, text, insertTransaction) => {\n    if (view.composing || view.state.readOnly || from != to || (text != \">\" && text != \"/\") ||\n        !xmlLanguage.isActiveAt(view.state, from, -1))\n        return false;\n    let base = insertTransaction(), { state } = base;\n    let closeTags = state.changeByRange(range => {\n        var _a, _b, _c;\n        let { head } = range;\n        let didType = state.doc.sliceString(head - 1, head) == text;\n        let after = syntaxTree(state).resolveInner(head, -1), name;\n        if (didType && text == \">\" && after.name == \"EndTag\") {\n            let tag = after.parent;\n            if (((_b = (_a = tag.parent) === null || _a === void 0 ? void 0 : _a.lastChild) === null || _b === void 0 ? void 0 : _b.name) != \"CloseTag\" &&\n                (name = elementName(state.doc, tag.parent, head))) {\n                let to = head + (state.doc.sliceString(head, head + 1) === \">\" ? 1 : 0);\n                let insert = `</${name}>`;\n                return { range, changes: { from: head, to, insert } };\n            }\n        }\n        else if (didType && text == \"/\" && after.name == \"StartCloseTag\") {\n            let base = after.parent;\n            if (after.from == head - 2 && ((_c = base.lastChild) === null || _c === void 0 ? void 0 : _c.name) != \"CloseTag\" &&\n                (name = elementName(state.doc, base, head))) {\n                let to = head + (state.doc.sliceString(head, head + 1) === \">\" ? 1 : 0);\n                let insert = `${name}>`;\n                return {\n                    range: EditorSelection.cursor(head + insert.length, -1),\n                    changes: { from: head, to, insert }\n                };\n            }\n        }\n        return { range };\n    });\n    if (closeTags.changes.empty)\n        return false;\n    view.dispatch([\n        base,\n        state.update(closeTags, {\n            userEvent: \"input.complete\",\n            scrollIntoView: true\n        })\n    ]);\n    return true;\n});\n\nexport { autoCloseTags, completeFromSchema, xml, xmlLanguage };\n"], "names": [], "sourceRoot": ""}